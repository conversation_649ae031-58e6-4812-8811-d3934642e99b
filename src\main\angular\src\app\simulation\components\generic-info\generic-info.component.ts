import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Child,
  Inject,
  AfterViewInit,
} from "@angular/core";
import { Activated<PERSON>oute, Para<PERSON>, Router } from "@angular/router";
import { NgForm } from "@angular/forms";
import { Observable } from "rxjs/Observable";
import { Subscription } from "rxjs/Subscription";
import { DomainService } from "../../../shared/domain/domain.service";
import { GenericInfoService } from "../../services/generic-info/generic-info.service";
import { GenericTaskService } from "../../../tasks/services/generic-task/generic-task.service";
import { Simulation } from "../../model/simulation";
import { LandingService } from "../../services/landing/landing.service";
import { MenuService } from "../../../shared/menu/services/menu.service";
import { PropertiesService } from "../../../shared/properties/properties.service";
import { AccessRightsService } from "../../../shared/access-rights/services/access-rights.service";
import { APP_CONSTANTS, IAppConstants } from "../../../app.constants";
import { UserDataService } from "../../../shared/user-data/user-data.service";
import { UserData } from "../../../shared/user-data/user-data";

@Component({
  selector: "app-generic-info",
  templateUrl: "./generic-info.component.html",
  styleUrls: ["./generic-info.component.css"],
})
export class GenericInfoComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild("f") form: NgForm;
  @ViewChild("appExpertTable") appExpertTable;
  simulation = new Simulation();
  wizardCode: string;
  positionId: string;
  threshold: number;
  currentTask: string;
  domainStructureType: any;
  domainScopeType: any;
  domainOperationType: any;
  domainAppraisalType: any;
  domainCollateralTecForm: any;
  domainMacroProcess: any;
  agencyList: any[] = [];
  expertList: any[] = [];
  private appraisalTypeFilters: any;
  private appraisalTypeCompleteDomain: any;
  appraisalOwnerDomain: any;
  fOperType = this.filterOperationType.bind(this);
  fApprType = this.filterAppraisalType.bind(this);
  draftButtonCallback = this.saveData.bind(this);
  contentLoaded = false;
  isSaveButtonEnabled = false;
  isUpdateMacroprocessAvailable = false;
  subscription: Subscription;
  userDetails: UserData;
  operationTypeCategory: string = "N";
  allowedScopeTypes: string[] = [];
  isExpertValid: boolean = true;
  macroprocessesCount = 0;
  accessPoint = "RP";
  flagForcingOwnerBool = false;
  isFlagForcingOwnerDisabled = false;
  macroProcess: any;
  objkey: any;
  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private domainService: DomainService,
    private genericInfoService: GenericInfoService,
    public landingService: LandingService,
    public menuService: MenuService,
    private propertiesService: PropertiesService,
    private _genericTaskService: GenericTaskService,
    private accessRightsService: AccessRightsService,
    private userDataService: UserDataService,
    private _expertAssignmentService: GenericTaskService,

    @Inject(APP_CONSTANTS) public constants: IAppConstants
  ) {}

  ngOnInit() {
    this.userDataService.getAll().subscribe((res: UserData) => {
      this.userDetails = res;
    });
    this.retrieveData();

    this.subscription = this.accessRightsService.authKeysSource.subscribe(
      (functions: string[]) => {
        this.isUpdateMacroprocessAvailable =
          functions &&
          functions.length > 0 &&
          functions.indexOf("UBZ_GENERIC_INFO_UPDATE_MACRO") > -1;
        this.checkMacroprocessLength();
      }
    );
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  ngAfterViewInit() {
    this.form.form.statusChanges.subscribe(() => {
      setTimeout(() => {
        this.updateSaveButtonEnabledState();
      }, 0);
    });
  }

  retrieveData() {
    this.activatedRoute.parent.params
      .switchMap((params: Params) => {
        this.wizardCode = params["wizardCode"];
        this.positionId = params["positionId"];

        this.currentTask =
          this.wizardCode === "WSIM" ? "UBZ-SIM-GEN" : "UBZ-REQ-GEN";
        return Observable.forkJoin(
          this.domainService.newGetDomain("UBZ_DOM_STRUCTURE_TYPE"),
          this.propertiesService.getInternalProperty(
            "THRESHOLD",
            this.positionId
          ),
          this.genericInfoService.getGenericInfo(
            this.positionId,
            this.wizardCode
          ),
          this.genericInfoService.getAppraisalTypeFilters()
        );
      })
      .switchMap((res) => {
        this.simulation = res[2];
        this.macroProcess = this.simulation.macroProcess;
        // Imposta originationProcess nel landingService e scatena il metodo di controllo task
        this.landingService.originationProcess =
          this.simulation.originationProcess;
        // Individua e setta il tipo di ndg
        if (res[2]["corporate"] === true) {
          this.landingService.posSegment = "COR";
        }
        if (res[2]["corporate"] === false) {
          this.landingService.posSegment = "IND";
        }
        this.landingService.checkIfCompletedTask(
          this.positionId,
          this.currentTask
        );
        this.domainStructureType = res[0];
        this.threshold = parseInt(res[1], 10);
        if (!this.simulation.macroProcess) {
          this.simulation.macroProcess = undefined;
        }
        if (!this.simulation.appraisalStruct) {
          this.simulation.appraisalStruct = undefined;
        }
        if (!this.simulation.appraisalScope) {
          this.simulation.appraisalScope = undefined;
        }
        if (!this.simulation.collatTecForm) {
          this.simulation.collatTecForm = undefined;
        }
        if (!this.simulation.operationType) {
          this.simulation.operationType = undefined;
        }
        if (!this.simulation.appraisalType) {
          this.simulation.appraisalType = undefined;
        }
        this.appraisalTypeFilters = res[3];
        if (this.isSurroga()) {
          this.simulation.flagPeriziaTerzi = true;
          this.simulation.appraisalType = "SUR";
          this.simulation.operationType = "SUR";
        }
        // controllo quali operation type chiedere al BE
        if (this.isSurroga()) this.operationTypeCategory = "Y";
        if (this.isTecnicaUsm()) this.operationTypeCategory = "U";
        if (this.isTecnicaOper()) this.operationTypeCategory = "O";
        if (this.isTecnicaUsmOper()) {
          this.simulation.flagPeriziaTerzi = true;
        }
        if (this.simulation.flagForcingOwner === "Y") {
          this.flagForcingOwnerBool = true;
        } else {
          this.flagForcingOwnerBool = false;
        }
        // salvo l'accesso
        if (this.simulation.accessPoint) {
          this.accessPoint = this.simulation.accessPoint;
        } else if (this.isTecnica()) {
          this.accessPoint = "PT";
         
        }
        if (this.isSurroga()) this.accessPoint = "SU";
        // menu perizia tecnica: flag perizia tecnica acceso e bloccato
        if (this.accessPoint === "PT") {
          this.simulation.flagPeriziaTerzi = true;
          if (this.isTecnicaUsmOper()) {
            this.simulation.flagForcingOwner = "N";
            this.flagForcingOwnerBool = false;
            this.isFlagForcingOwnerDisabled = true;
          }
            else {
            this.simulation.flagForcingOwner = "Y";
            this.flagForcingOwnerBool = true;
            this.isFlagForcingOwnerDisabled = true;
          }
          
        }

        if (this.accessPoint === "RP") {
        if (this.isTecnicaUsmOper()) {
            this.simulation.flagForcingOwner = 'N';
            this.flagForcingOwnerBool = true;
            this.isFlagForcingOwnerDisabled = true;
          } 
          
        
        }
        return this.getData();
      })
      .subscribe((res) => {
        this.domainCollateralTecForm = res[0];
        this.domainOperationType = res[1];
        // use for ngClass in template
        this.objkey = Object.keys(this.domainOperationType).find(
          (key) => this.domainOperationType[key].domCode === "REL"
        );
        // use for ngClass in template
        this.appraisalTypeCompleteDomain = res[2];
        this.domainAppraisalType = this.appraisalTypeCompleteDomain;
        this.domainScopeType = res[3];
        this.appraisalOwnerDomain = res[4];
        this.getAppraisalMacroprocessAvailable();
        if (!this.simulation.fromaSim) {
          this.agencyList = res[5].listExpSocPer;
          this.expertList = res[6].listExpSocPer;

          if (!this.simulation.expertName) {
            this.simulation.expertName = undefined;
          }
        }
        if (this.isTecnicaOper()) {
          this.isExpertValid = false;
        }
        this.setInitialExpert();
        this.contentLoaded = true;
      });
  }

  getAppraisalMacroprocessAvailable() {
    const externalAppraisalFlag =
      this.simulation.flagPeriziaTerzi || this.isTecnicaUsmOper() === true
        ? "Y"
        : "N";
    const posSegment = this.landingService.posSegment;
    this.genericInfoService
      .getAppraisalMacroprocessAvailable(
        externalAppraisalFlag,
        this.accessPoint,
        posSegment
      )
      .subscribe((response) => {
        this.domainMacroProcess = response;
        this.checkMacroprocessLength();
      });
  }

  checkMacroprocessLength() {
    if (
      typeof this.domainMacroProcess !== "undefined" &&
      this.domainMacroProcess
    ) {
      this.macroprocessesCount = Object.keys(this.domainMacroProcess).length;
      let macroprocessDefault = "";
      // if macroprocess contains AGG and cannot display it
      // remove 1 from macroprocessesCount
      if (!this.isUpdateMacroprocessAvailable) {
        Object.keys(this.domainMacroProcess).forEach((key: any) => {
          if (
            this.domainMacroProcess[key]["domCode"] &&
            this.domainMacroProcess[key]["domCode"] === "AGG"
          ) {
            this.macroprocessesCount--;
          } else {
            macroprocessDefault = this.domainMacroProcess[key]["domCode"];
          }
        });
      }
      if (this.macroprocessesCount === 1) {
        this.simulation.macroProcess =
          macroprocessDefault !== ""
            ? macroprocessDefault
            : this.domainMacroProcess[Object.keys(this.domainMacroProcess)[0]][
                "domCode"
              ];
      } else {
        let macroProcessFound = false;
        Object.keys(this.domainMacroProcess).forEach((key: any) => {
          if (
            this.domainMacroProcess[key]["domCode"] &&
            this.domainMacroProcess[key]["domCode"] ===
              this.simulation.macroProcess
          ) {
            macroProcessFound = true;
          }
        });
        if (!macroProcessFound) {
          this.simulation.macroProcess = "";
        }
      }
    }
  }

  setInitialExpert() {
    if (this.isTecnicaOper()) {
      if (this.simulation.operationType === "RTE") {
        setTimeout(() => {
          this.appExpertTable.lockPrelios();
        }, 10);
      } else {
        setTimeout(() => {
          this.appExpertTable.unlockPrelios();
        }, 10);
      }
    }
  }
  // Esegue le chiamate ai domini per popolare le relative select
  private getData(): Observable<any> {
    if (!this.simulation.fromaSim) {
      return Observable.forkJoin(
        this.domainService.newGetDomain(
          "UBZ_DOM_COLLAT_TEC_FORM",
          this.simulation.assetType
        ),
        this.domainService.newGetDomain(
          "UBZ_DOM_OPERATION_TYPE",
          this.operationTypeCategory
        ),
        this.domainService.newGetDomain(
          "UBZ_DOM_APPRAISAL_TYPE",
          this.isSurroga() ? "Y" : "N"
        ),
        this.domainService.newGetDomain(
          "UBZ_DOM_SCOPE_TYPE",
          this.simulation.assetType
        ),
        this.domainService.newGetDomain("UBZ_DOM_APPRAISAL_OWNER"),
        this._genericTaskService.getExpertList(this.positionId, "SOC", 0, 0),
        this._genericTaskService.getExpertList(this.positionId, "PBN", 0, 0)
      );
    } else {
      return Observable.forkJoin(
        this.domainService.newGetDomain(
          "UBZ_DOM_COLLAT_TEC_FORM",
          this.simulation.assetType
        ),
        this.domainService.newGetDomain(
          "UBZ_DOM_OPERATION_TYPE",
          this.operationTypeCategory
        ),
        this.domainService.newGetDomain(
          "UBZ_DOM_APPRAISAL_TYPE",
          this.isSurroga() ? "Y" : "N"
        ),
        this.domainService.newGetDomain(
          "UBZ_DOM_SCOPE_TYPE",
          this.simulation.assetType
        ),
        this.domainService.newGetDomain("UBZ_DOM_APPRAISAL_OWNER")
      );
    }
  }

  changePeriziaDaTerzi() {
    this.simulation.forcingOwner = null;
    this.simulation.flagPeriziaTerzi = !this.simulation.flagPeriziaTerzi;
    if (!this.isSurroga()) {
      if (!this.simulation.flagPeriziaTerzi) {
        this.simulation.operationType = undefined;
        this.simulation.expertName = undefined;
        this.simulation.surveyOutcome = undefined;
      }
      this.getAppraisalMacroprocessAvailable();
    }
    

 if(this.accessPoint ==='RP'){
            this.simulation.flagForcingOwner = 'Y';
            this.flagForcingOwnerBool = true;
            this.isFlagForcingOwnerDisabled = true;
          }
   
  }

  changeFlagForcingOwner() {
    this.flagForcingOwnerBool = !this.flagForcingOwnerBool;
    if (!this.flagForcingOwnerBool) {
      this.simulation.forcingOwner = null;
      this.simulation.forcingNote = null;
      this.simulation.flagForcingOwner = "N";
    } else {
      this.simulation.flagForcingOwner = "Y";
    }
  }

  changeOperationType() {
    if (this.isTecnicaOper()) {
      if (this.simulation.operationType === "RTE") {
        this.appExpertTable.lockPrelios();
      } else {
        this.appExpertTable.unlockPrelios();
      }
    }
    if (this.isTecnicaUsmOper()) return;
    if (this.simulation.operationType !== "REL") {
      this.simulation.expertName = undefined;
    }
  }

  saveGenericInfo() {
    if (this.form.valid) {
      if (this.simulation.surveyOutcome) {
        this.simulation.surveyOutcome = this.simulation.surveyOutcome.valueOf();
      }
      this.genericInfoService
        .saveGenericInfo(this.simulation, this.wizardCode)
        .subscribe((result) => {
          if (this.isTecnicaOper()) {
            //Save expert
            this.appExpertTable.saveExpert();
          } else if (this.isTecnicaUsm()) {
            // Invoca il servizio che restituisce il corretto codice prelios a seconda
            // dell'ambiente nel quale ci si trova
            this._expertAssignmentService
              .getThirdNetworkProperty()
              .subscribe((preliosCod) => {
                if (preliosCod) {
                  this._expertAssignmentService
                    .saveExpert(this.simulation.positionId, "SOC", preliosCod)
                    .subscribe((result) => {});
                }
              });
          }
          this.landingService.goNextPage(
            this.simulation.positionId,
            this.currentTask,
            this.wizardCode,
            this.activatedRoute
          );
        });
    }
  }

  filterOperationType(item): boolean {
    return item.domCode !== "REL";
  }

  filterAppraisalType(item) {
    let filteredDomain: any[];
    if (this.isTecnicaUsm()) {
      if (this.isTecnicaExternal()) {
        filteredDomain = this.appraisalTypeFilters["ASS"];
      } else {
        filteredDomain = this.appraisalTypeFilters["PER"];
      }
    } else if (this.isTecnicaOper()) {
      if (this.simulation.operationType === "MDI") {
        filteredDomain = this.appraisalTypeFilters["RSF"];
      } else {
        filteredDomain = this.appraisalTypeFilters["PER"];
      }
    } else if (
      this.simulation.macroProcess === "AGG" ||
      this.simulation.macroProcess === "WOR"
    ) {
      filteredDomain = this.appraisalTypeFilters["PER"];
    } else if (this.isTecnicaMintLosExternalInd()) {
      filteredDomain = this.appraisalTypeFilters["ASS"];
    } else if (
      this.simulation.corporate &&
      this.simulation.assetType === "IMM"
    ) {
      filteredDomain = this.appraisalTypeFilters["RSF"];
    } else if (
      this.simulation.originationProcess !== "UB6" &&
      this.simulation.originationProcess !== "EMP" &&
      this.simulation.originationProcess !== "TGP"
    ) {
      filteredDomain = this.appraisalTypeFilters["RSF"];
    }
    if (!filteredDomain || filteredDomain.length === 0) {
      return true;
    } else {
      for (const i in filteredDomain) {
        if (item.domCode === filteredDomain[i].domCode) {
          return true;
        }
      }
      return false;
    }
  }

  previous() {
    this.landingService.goToPreviousTask(
      this.positionId,
      this.wizardCode,
      this.activatedRoute
    );
  }

  saveData(): Observable<any> {
    return this.genericInfoService.saveGenericInfo(
      this.simulation,
      this.wizardCode
    );
  }

  exit() {
    this.router.navigate(["/"]);
  }

  cancelPosition() {
    this.landingService
      .cancelPosition(this.simulation.positionId, this.wizardCode)
      .subscribe((res) => this.router.navigate(["/"]));
  }

  isSurroga() {
    return (
      this.wizardCode === this.constants.wizardCodes.REQ &&
      this.simulation.originationProcess === "SUR"
    );
  }

  isTecnicaUsmOper() {
    return (
      this.isTecnica() &&
      this.landingService.posSegment === "IND" &&
      (this.isUbzUsmProfile() || this.isUbzOperProfile())
    );
  }

  isTecnicaUsm() {
    return (
      this.isTecnica() &&
      this.landingService.posSegment === "IND" &&
      this.isUbzUsmProfile()
    );
  }

  isTecnicaMintLosExternalInd() {
    // Controllo per:
    // tecnica esterna
    // individuals
    // USM, UBZLOS, UBZRLOS, UBZMINT, UBZRMINT, UBZSMINT
    return (
      this.isTecnicaExternal() &&
      this.landingService.posSegment === "IND" &&
      this.isUbzMintOrLosProfile()
    );
  }

  isTecnicaOper() {
    return (
      this.isTecnica() &&
      this.landingService.posSegment === "IND" &&
      this.isUbzOperProfile()
    );
  }

  isFieldSetDisabled() {
    return (
      this.landingService.isLockedTask[this.currentTask] ||
      this.landingService.originationProcess === "TGP"
    );
  }

  isTecnicaOperETL() {
    return (
      !this.isFieldSetDisabled() &&
      this.simulation.macroProcess === "ETL" &&
      this.landingService.posSegment === "IND" &&
      this.isUbzOperProfile()
    );
  }

  isTecnica() {
    return (
      this.simulation.macroProcess === "CTE" ||
      this.simulation.macroProcess === "ITL" ||
      this.simulation.macroProcess === "ETL"
    );
  }

  isTecnicaExternal() {
    return this.simulation.macroProcess === "ETL";
  }

  isUbzUsmProfile() {
    let usmProfiles: string[] = ["PRZUSM", "UBZUSM"];
    return usmProfiles.indexOf(this.userDetails.profile) >= 0;
  }

  isUbzOperProfile() {
    let operProfiles: string[] = ["PRZOPER", "UBZOPER"];
    return operProfiles.indexOf(this.userDetails.profile) >= 0;
  }

  isUbzMintOrLosProfile() {
    let mintOrLosProfiles: string[] = [
      "UBZLOS",
      "UBZRLOS",
      "UBZMINT",
      "UBZRMINT",
      "UBZSMINT",
      "UBZRST",
    ];
    return mintOrLosProfiles.indexOf(this.userDetails.profile) >= 0;
  }

  isUbzRstProfile() {
    return this.userDetails.profile === "UBZRST";
  }

  onIsCompiledExpert() {
    this.isExpertValid = true;
    this.updateSaveButtonEnabledState();
  }

  onIsNotCompiledExpert() {
    this.isExpertValid = false;
    this.updateSaveButtonEnabledState();
  }

  updateSaveButtonEnabledState() {
    this.isSaveButtonEnabled =
      this.form.valid && this.simulation && this.isExpertValid && true;
  }
  // Metodo per impostare le variabili quando cambia il macroprocesso
  changeMacroProcess() {
    if (this.simulation.macroProcess === "AGG") {
      this.simulation.appraisalScope = "AGG";
      this.simulation.appraisalType = "PER";
      this.simulation.flagPeriziaTerzi = false;
    } else {
      this.simulation.appraisalScope = undefined;
      this.simulation.appraisalType = undefined;
    }
  }
}
